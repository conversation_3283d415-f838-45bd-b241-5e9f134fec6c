package com.ruoyi.core.service;

import cn.hutool.core.util.StrUtil;
import com.ruoyi.core.mapper.GenericMapper;
import org.springframework.jdbc.core.namedparam.NamedParameterJdbcTemplate;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.stream.Collectors;

/**
 * 药卡
 */
@Service
public class MedicationService {
    private final GenericMapper genericMapper;
    private final NamedParameterJdbcTemplate namedParameterJdbcTemplate;
    private static final String TABLE_RECORD = "data_ext_menzbl";
    private static final String TABLE_ORDER_1 = "medication_order";
    private static final String TABLE_ORDER_2 = "external_rx";

    public MedicationService(GenericMapper genericMapper, NamedParameterJdbcTemplate namedParameterJdbcTemplate) {
        this.genericMapper = genericMapper;
        this.namedParameterJdbcTemplate = namedParameterJdbcTemplate;
    }

    /**
     * 根据住院号查询医嘱
     *
     * @param hid 住院号
     * @return 医嘱信息
     */
    public Map<String, Object> getByHID(String hid) {
        List<Map<String, Object>> recordList = genericMapper.getList(
                TABLE_RECORD,
                "leaf_id,zhuyuanh,xingming,quy,chuangw_no,nianling,xingbie",
                List.<String[]>of(new String[]{"equal", "zhuyuanh", hid}),
                "order by leaf_id desc limit 1"
        );
        if (recordList.isEmpty()) {
            return Map.of();
        }
        Map<String, Object> record = recordList.get(0);

        List<Map<String, Object>> patientList = genericMapper.getList(
                MedicalHistoryService.TABLE_PATIENT,
                "old_num,bed_name",
                List.<String[]>of(new String[]{"equal", "old_num", (String) record.get("zhuyuanh")}),
                "limit 1"
        );
        if (!patientList.isEmpty()) {
            record.put("bed_name", patientList.get(0).get("bed_name"));
        }

        List<Map<String, Object>> order1List = genericMapper.getList(
                TABLE_ORDER_1,
                "*",
                List.<String[]>of(new String[]{"equal", "medical_record_id", (String) record.get("leaf_id")}),
                ""
        );
        List<Map<String, Object>> order2List = genericMapper.getList(
                TABLE_ORDER_2,
                "*",
                List.<String[]>of(new String[]{"equal", "medical_record_id", (String) record.get("leaf_id")}),
                ""
        );

        List<Map<String, Object>> orderList = new ArrayList<>();
        orderList.addAll(order1List);
        orderList.addAll(order2List);

        if (orderList.isEmpty()) {
            return record;
        }

        orderList.sort(Comparator.comparing(
                map -> (String) map.get("drug_stock_id"),
                Comparator.nullsLast(String::compareTo)
        ));

        record.put("order", orderList);

        return record;
    }

    public List<Map<String, Object>> getList(String sn, String name, String area, String bed, String skip, String take) {
        List<String[]> condition = new ArrayList<>();
        if (sn != null && !sn.isBlank()) {
            condition.add(new String[]{"equal", "zhuyuanh", sn});
        }
        if (name != null && !name.isBlank()) {
            condition.add(new String[]{"equal", "xingming", name});
        }
        if (area != null && !area.isBlank()) {
            condition.add(new String[]{"equal", "quy", area});
        }
        if (bed != null && !bed.isBlank()) {
            condition.add(new String[]{"equal", "chuangw_no", bed});
        }
        List<Map<String, Object>> recordList = genericMapper.getList(
                TABLE_RECORD,
                "leaf_id,zhuyuanh,xingming,quy,chuangw_no",
                condition,
                "limit " + skip + "," + take
        );
        if (recordList.isEmpty()) {
            return List.of();
        }

        List<String> recordIDs = recordList.stream()
                .map(map -> (String) map.get("leaf_id"))
                .distinct()
                .toList();

        List<Map<String, Object>> order1List = genericMapper.getList(
                TABLE_ORDER_1,
                "*",
                List.of(
                        new String[]{"in", "medical_record_id", String.join(",", recordIDs)},
                        new String[]{"in", "order_type", "长期,临时"}
                ),
                ""
        );
        List<Map<String, Object>> order2List = genericMapper.getList(
                TABLE_ORDER_2,
                "*",
                List.of(
                        new String[]{"in", "medical_record_id", String.join(",", recordIDs)},
                        new String[]{"in", "order_type", "长期,临时"}
                ),
                ""
        );
        List<Map<String, Object>> orderList = new ArrayList<>();
        orderList.addAll(order1List);
        orderList.addAll(order2List);

        if (orderList.isEmpty()) {
            return List.of();
        }

        orderList.sort(Comparator.comparing(
                map -> (String) map.get("drug_stock_id"),
                Comparator.nullsLast(String::compareTo)
        ));

        Map<String, Map<String, Object>> recordMap = recordList.stream()
                .collect(Collectors.toMap(
                        map -> (String) map.get("leaf_id"),
                        map -> map
                ));

        orderList.forEach(map -> {
            String recordId = (String) map.get("medical_record_id");
            if (recordMap.containsKey(recordId)) {
                Map<String, Object> record = recordMap.get(recordId);
                map.put("record", record);
            }
        });

        return orderList;
    }

    public Object areaList() {
        String sql = """
                select leaf_id,quymc from data_ext_quysz order by c_time desc,leaf_id desc
                """;
        return namedParameterJdbcTemplate.queryForList(sql, new HashMap<>());
    }

    public Object patientList(String query) {
        StringBuilder sql = new StringBuilder("""
                SELECT
                    leaf_id,
                    CONCAT(old_bed, '|', old_name, '|', DATE_FORMAT(enter_time, '%Y-%m-%d')) AS patientInfo
                FROM
                    data_ext_p000001321020140
                where 1=1
                """);
        Map<String, Object> params = new HashMap<>();
        if (StrUtil.isNotBlank(query)) {
            sql.append(" and CONCAT(old_bed, old_name, DATE_FORMAT(enter_time, '%Y-%m-%d')) LIKE :query");
            params.put("query", "%" + query + "%");
        }
        sql.append(" LIMIT 20");
        return namedParameterJdbcTemplate.queryForList(sql.toString(), params);
    }

    public Object getPatientInfo(String id) {
        String sql = """
                select leaf_id,old_name as xingming, old_bed as chuangw_no, old_num as zhuyuanh, old_age as nianling,
                old_sex as xingbie, class_name as quy,'' as yibaolx from data_ext_p000001321020140
                where leaf_id = :id
                """;
        return namedParameterJdbcTemplate.queryForMap(sql, Map.of("id", id));
    }

    /**
     * @description 生成药卡
     * <AUTHOR>
     */
    public Object generate(String queryDate) {
        if (StrUtil.isBlank(queryDate)) {
            return Map.of("code", 400, "msg", "查询日期不能为空");
        }

        try {
            // 解析查询日期
            java.time.LocalDate targetDate = java.time.LocalDate.parse(queryDate);
            java.time.DayOfWeek dayOfWeek = targetDate.getDayOfWeek();
            String weekDayName = getChineseWeekDay(dayOfWeek);

            List<Map<String, Object>> result = new ArrayList<>();

            // 1. 查询所有长期医嘱（status=2，已核对）
            List<Map<String, Object>> longTermOrders = getLongTermOrders();

            // 2. 处理用药医嘱
            List<Map<String, Object>> medicationOrders = getMedicationOrders(longTermOrders);
            for (Map<String, Object> order : medicationOrders) {
                if (shouldShowOnDate(order, targetDate, weekDayName)) {
                    Map<String, Object> medicationCard = buildMedicationCard(order);
                    if (medicationCard != null) {
                        result.add(medicationCard);
                    }
                }
            }

            // 3. 处理外配药医嘱
            List<Map<String, Object>> externalRxOrders = getExternalRxOrders(longTermOrders);
            for (Map<String, Object> order : externalRxOrders) {
                if (shouldShowOnDate(order, targetDate, weekDayName) && hasEnoughStock(order)) {
                    Map<String, Object> medicationCard = buildMedicationCard(order);
                    if (medicationCard != null) {
                        result.add(medicationCard);
                    }
                }
            }

            // 按住院号排序
            result.sort((a, b) -> {
                String hospitalIdA = (String) a.get("hospitalizationId");
                String hospitalIdB = (String) b.get("hospitalizationId");
                if (hospitalIdA == null && hospitalIdB == null) return 0;
                if (hospitalIdA == null) return 1;
                if (hospitalIdB == null) return -1;
                return hospitalIdA.compareTo(hospitalIdB);
            });

            return Map.of("code", 200, "data", result, "total", result.size());

        } catch (Exception e) {
            return Map.of("code", 500, "msg", "生成药卡失败: " + e.getMessage());
        }
    }

    /**
     * 获取所有长期医嘱（status=2，已核对）
     */
    private List<Map<String, Object>> getLongTermOrders() {
        String sql = """
                SELECT medication_order_id, order_type, order_name
                FROM doctor_order
                WHERE status = 2
                AND order_type = '长期'
                AND is_deleted = 0
                AND (order_name = '用药医嘱' or order_name = '外配药医嘱')
                """;
        return namedParameterJdbcTemplate.queryForList(sql, new HashMap<>());
    }

    /**
     * 获取用药医嘱（口服给药途径，排除已停药的医嘱）
     */
    private List<Map<String, Object>> getMedicationOrders(List<Map<String, Object>> longTermOrders) {
        if (longTermOrders.isEmpty()) {
            return new ArrayList<>();
        }

        // 过滤出用药医嘱类型的订单ID
        List<String> medicationOrderIds = longTermOrders.stream()
                .filter(order -> "用药医嘱".equals(order.get("order_name")))
                .map(order -> (String) order.get("medication_order_id"))
                .distinct()
                .toList();

        if (medicationOrderIds.isEmpty()) {
            return new ArrayList<>();
        }

        String sql = """
                SELECT mo.*, mb.xingming, mb.zhuyuanh, mb.chuangw_no
                FROM medication_order mo
                LEFT JOIN data_ext_menzbl mb ON mo.medical_record_id = mb.leaf_id
                LEFT JOIN out_stop_medicine osm ON mo.id = osm.medication_order_id
                    AND osm.status = 1
                    AND osm.is_deleted = 0
                WHERE mo.id IN (:orderIds)
                AND mo.administration_route = '口服'
                AND mo.is_deleted = 0
                AND osm.id IS NULL
                """;

        Map<String, Object> params = new HashMap<>();
        params.put("orderIds", medicationOrderIds);
        return namedParameterJdbcTemplate.queryForList(sql, params);
    }

    /**
     * 获取外配药医嘱（口服给药途径，排除已停药的医嘱）
     */
    private List<Map<String, Object>> getExternalRxOrders(List<Map<String, Object>> longTermOrders) {
        if (longTermOrders.isEmpty()) {
            return new ArrayList<>();
        }

        // 过滤出外配药医嘱类型的订单ID
        List<String> externalRxOrderIds = longTermOrders.stream()
                .filter(order -> "外配药医嘱".equals(order.get("order_name")))
                .map(order -> (String) order.get("medication_order_id"))
                .distinct()
                .toList();

        if (externalRxOrderIds.isEmpty()) {
            return new ArrayList<>();
        }

        String sql = """
                SELECT er.*, mb.xingming, mb.zhuyuanh, mb.chuangw_no
                FROM external_rx er
                LEFT JOIN data_ext_menzbl mb ON er.medical_record_id = mb.leaf_id
                LEFT JOIN out_stop_medicine osm ON er.id = osm.medication_order_id
                    AND osm.status = 1
                    AND osm.is_deleted = 0
                WHERE er.id IN (:orderIds)
                AND er.administration_route = '口服'
                AND er.is_deleted = 0
                AND osm.id IS NULL
                """;

        Map<String, Object> params = new HashMap<>();
        params.put("orderIds", externalRxOrderIds);
        return namedParameterJdbcTemplate.queryForList(sql, params);
    }

    /**
     * 判断是否应该在指定日期显示
     */
    private boolean shouldShowOnDate(Map<String, Object> order, java.time.LocalDate targetDate, String weekDayName) {
        String administrationMethod = (String) order.get("administration_method");
        if (StrUtil.isBlank(administrationMethod)) {
            return false;
        }

        administrationMethod = administrationMethod.toUpperCase();

        // 简单的每日给药方法，需要检查是否在医嘱开始日期之后
        if (administrationMethod.equals("QD") || administrationMethod.equals("BID") ||
            administrationMethod.equals("TID") || administrationMethod.equals("QID") ||
            administrationMethod.equals("Q4H") || administrationMethod.equals("Q6H") ||
            administrationMethod.equals("Q8H") || administrationMethod.equals("Q12H") ||
            administrationMethod.equals("QN")) {

            java.time.LocalDate orderDate = getOrderDate(order);
            if (orderDate == null) {
                // 如果无法获取医嘱日期，默认显示
                return true;
            }
            // 只有在医嘱开始日期当天或之后才显示
            return !targetDate.isBefore(orderDate);
        }

        // 复杂的给药方法需要计算
        java.time.LocalDate orderDate = getOrderDate(order);
        if (orderDate == null) {
            return false;
        }

        switch (administrationMethod) {
            case "QOD": // 隔天一次
                return calculateQodDisplay(orderDate, targetDate);
            case "QW": // 一周一次
            case "BIW": // 一周两次
            case "TIW": // 一周三次
                return calculateWeeklyDisplay(order, targetDate, weekDayName);
            default:
                return false;
        }
    }

    /**
     * 获取医嘱开始日期
     */
    private java.time.LocalDate getOrderDate(Map<String, Object> order) {
        // 优先使用 prescription_date
        Object orderDateObj = order.get("prescription_date");
        if (orderDateObj == null) {
            return null;
        }

        try {
            // 添加调试信息
            System.out.println("DEBUG: orderDateObj类型: " + orderDateObj.getClass().getName() + ", 值: " + orderDateObj);

            if (orderDateObj instanceof java.sql.Date) {
                return ((java.sql.Date) orderDateObj).toLocalDate();
            } else if (orderDateObj instanceof java.time.LocalDate) {
                return (java.time.LocalDate) orderDateObj;
            } else if (orderDateObj instanceof java.time.LocalDateTime) {
                return ((java.time.LocalDateTime) orderDateObj).toLocalDate();
            } else if (orderDateObj instanceof String) {
                String dateStr = (String) orderDateObj;
                // 处理不同的日期格式
                if (dateStr.contains("T")) {
                    // ISO 8601 格式
                    try {
                        return java.time.LocalDateTime.parse(dateStr).toLocalDate();
                    } catch (Exception e) {
                        // 如果解析失败，尝试其他格式
                        if (dateStr.length() > 19) {
                            // 可能包含毫秒，截取到秒
                            dateStr = dateStr.substring(0, 19);
                            return java.time.LocalDateTime.parse(dateStr).toLocalDate();
                        }
                        throw e;
                    }
                } else if (dateStr.contains(" ")) {
                    // 如果包含空格分隔的时间部分，只取日期部分
                    dateStr = dateStr.split(" ")[0];
                    return java.time.LocalDate.parse(dateStr);
                } else {
                    // 纯日期格式：2025-07-20
                    return java.time.LocalDate.parse(dateStr);
                }
            } else {
                // 未知类型，尝试转换为字符串后解析
                System.out.println("DEBUG: 未知类型，尝试toString(): " + orderDateObj.toString());
                String dateStr = orderDateObj.toString();
                if (dateStr.contains("T")) {
                    return java.time.LocalDateTime.parse(dateStr).toLocalDate();
                } else if (dateStr.contains(" ")) {
                    dateStr = dateStr.split(" ")[0];
                    return java.time.LocalDate.parse(dateStr);
                } else {
                    return java.time.LocalDate.parse(dateStr);
                }
            }
        } catch (Exception e) {
            // 日期解析失败，记录日志但不抛出异常
            System.err.println("日期解析失败: " + orderDateObj + " (类型: " + orderDateObj.getClass().getName() + "), 错误: " + e.getMessage());
            e.printStackTrace();
        }
        return null;
    }

    /**
     * 计算隔天一次(QOD)是否在目标日期显示
     */
    private boolean calculateQodDisplay(java.time.LocalDate orderDate, java.time.LocalDate targetDate) {
        // 如果目标日期在医嘱开始日期之前，不显示
        if (targetDate.isBefore(orderDate)) {
            return false;
        }

        long daysBetween = java.time.temporal.ChronoUnit.DAYS.between(orderDate, targetDate);
        // 当天开的医嘱（daysBetween = 0）应该显示
        // 隔天一次：第0天、第2天、第4天...显示
        return daysBetween % 2 == 0;
    }

    /**
     * 计算每周给药是否在目标日期显示
     */
    private boolean calculateWeeklyDisplay(Map<String, Object> order, java.time.LocalDate targetDate, String weekDayName) {
        // 首先检查是否在医嘱开始日期之后
        java.time.LocalDate orderDate = getOrderDate(order);
        if (orderDate != null && targetDate.isBefore(orderDate)) {
            return false;
        }

        String weekDay = (String) order.get("week_day");
        if (StrUtil.isBlank(weekDay)) {
            return false;
        }

        // 解析week_day字段，格式如："一，二" 或 "一、二、三"
        String[] weekDays = weekDay.split("[，,、]");
        for (String day : weekDays) {
            if (day.trim().equals(weekDayName)) {
                return true;
            }
        }
        return false;
    }

    /**
     * 将英文星期转换为中文
     */
    private String getChineseWeekDay(java.time.DayOfWeek dayOfWeek) {
        return switch (dayOfWeek) {
            case MONDAY -> "一";
            case TUESDAY -> "二";
            case WEDNESDAY -> "三";
            case THURSDAY -> "四";
            case FRIDAY -> "五";
            case SATURDAY -> "六";
            case SUNDAY -> "日";
        };
    }

    /**
     * 检查外配药库存是否充足
     */
    private boolean hasEnoughStock(Map<String, Object> order) {
        String drugName = (String) order.get("drug_name");
        String zhuyuanh = (String) order.get("zhuyuanh");
        String xingming = (String) order.get("xingming");

        if (StrUtil.isBlank(drugName) || StrUtil.isBlank(zhuyuanh) || StrUtil.isBlank(xingming)) {
            return false;
        }

        String sql = """
                SELECT kucsl
                FROM data_ext_waipykc
                WHERE yaopmc = :drugName
                AND zhuyh = :zhuyuanh
                AND xingm = :xingming
                """;

        Map<String, Object> params = new HashMap<>();
        params.put("drugName", drugName);
        params.put("zhuyuanh", zhuyuanh);
        params.put("xingming", xingming);

        try {
            Map<String, Object> stockInfo = namedParameterJdbcTemplate.queryForMap(sql, params);
            String kucslStr = (String) stockInfo.get("kucsl");
            if (StrUtil.isNotBlank(kucslStr)) {
                double stock = Double.parseDouble(kucslStr);
                return stock > 0;
            }
        } catch (Exception e) {
            // 查询失败或没有库存记录，认为库存不足
        }
        return false;
    }

    /**
     * 构建药卡信息
     */
    private Map<String, Object> buildMedicationCard(Map<String, Object> order) {
        Map<String, Object> card = new HashMap<>();

        // 患者信息
        card.put("patientName", order.get("xingming"));
        card.put("hospitalizationId", order.get("zhuyuanh"));
        card.put("bedNumber", order.get("chuangw_no"));

        // 药品信息
        card.put("drugName", order.get("drug_name"));

        // 构建用法字符串（早中晚夜以及对应的时间）
        String usage = buildUsageString(order);
        card.put("usage", usage);

        return card;
    }

    /**
     * 构建用法字符串
     */
    private String buildUsageString(Map<String, Object> order) {
        StringBuilder usage = new StringBuilder();
        String singleDoseUnit = (String) order.get("single_dose_unit");
        if (StrUtil.isBlank(singleDoseUnit)) {
            singleDoseUnit = "";
        }

        // 早
        if (order.get("morning_quantity") != null &&
            ((Number) order.get("morning_quantity")).doubleValue() > 0) {
            usage.append("早").append(order.get("morning_quantity"));
            if (order.get("morning_time") != null) {
                usage.append("(").append(order.get("morning_time")).append(")");
            }
            usage.append(singleDoseUnit).append(" ");
        }

        // 中
        if (order.get("noon_quantity") != null &&
            ((Number) order.get("noon_quantity")).doubleValue() > 0) {
            usage.append("中").append(order.get("noon_quantity"));
            if (order.get("noon_time") != null) {
                usage.append("(").append(order.get("noon_time")).append(")");
            }
            usage.append(singleDoseUnit).append(" ");
        }

        // 晚
        if (order.get("evening_quantity") != null &&
            ((Number) order.get("evening_quantity")).doubleValue() > 0) {
            usage.append("晚").append(order.get("evening_quantity"));
            if (order.get("evening_time") != null) {
                usage.append("(").append(order.get("evening_time")).append(")");
            }
            usage.append(singleDoseUnit).append(" ");
        }

        // 夜
        if (order.get("night_quantity") != null &&
            ((Number) order.get("night_quantity")).doubleValue() > 0) {
            usage.append("夜").append(order.get("night_quantity"));
            if (order.get("night_time") != null) {
                usage.append("(").append(order.get("night_time")).append(")");
            }
            usage.append(singleDoseUnit).append(" ");
        }

        return usage.toString().trim();
    }
}
