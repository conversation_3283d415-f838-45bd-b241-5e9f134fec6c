/*
 Navicat Premium Data Transfer

 Source Server         : ************
 Source Server Type    : MySQL
 Source Server Version : 80018 (8.0.18)
 Source Host           : ************:33068
 Source Schema         : yzlc

 Target Server Type    : MySQL
 Target Server Version : 80018 (8.0.18)
 File Encoding         : 65001

 Date: 27/07/2025 14:58:13
*/

SET NAMES utf8mb4;
SET FOREIGN_KEY_CHECKS = 0;

-- ----------------------------
-- Table structure for medication_order
-- ----------------------------
DROP TABLE IF EXISTS `medication_order`;
CREATE TABLE `medication_order`  (
  `id` bigint(20) NOT NULL COMMENT '主键ID（雪花ID）',
  `medical_record_id` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '门诊病历id',
  `prescription_number` bigint(20) NULL DEFAULT NULL COMMENT '处方号',
  `prescription_date` datetime NULL DEFAULT NULL COMMENT '处方日期',
  `drug_stock_id` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '' COMMENT '药品库方id',
  `drug_name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT '' COMMENT '药品名称',
  `drug_spec` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT '' COMMENT '药品规格',
  `drug_unit` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT '' COMMENT '药品单位',
  `status` int(11) NOT NULL COMMENT '状态',
  `part` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT '' COMMENT '部位',
  `administration_route` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT '' COMMENT '给药途径',
  `administration_method` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT '' COMMENT '给药方法',
  `prescription_quantity` int(11) NULL DEFAULT NULL COMMENT '处方数量',
  `packaging_quantity` int(11) NULL DEFAULT NULL COMMENT '包装量',
  `packaging_unit` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT '' COMMENT '包装单位',
  `week_day` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT '' COMMENT '星期',
  `morning_quantity` decimal(10, 2) NULL DEFAULT NULL COMMENT '早（数量）',
  `morning_time` time NULL DEFAULT NULL COMMENT '早（时间）',
  `noon_quantity` decimal(10, 2) NULL DEFAULT NULL COMMENT '中（数量）',
  `noon_time` time NULL DEFAULT NULL COMMENT '中（时间）',
  `evening_quantity` decimal(10, 2) NULL DEFAULT NULL COMMENT '晚（数量）',
  `evening_time` time NULL DEFAULT NULL COMMENT '晚（时间）',
  `night_quantity` decimal(10, 2) NULL DEFAULT NULL COMMENT '夜（数量）',
  `night_time` time NULL DEFAULT NULL COMMENT '夜（时间）',
  `prompt` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT '' COMMENT '提示',
  `unit_price` decimal(10, 4) NULL DEFAULT NULL COMMENT '单价',
  `single_dose` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT '' COMMENT '次用量',
  `single_dose_unit` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '次用量单位',
  `order_date` date NULL DEFAULT NULL COMMENT '医嘱日期',
  `attending_doctor_id` bigint(20) NULL DEFAULT NULL COMMENT '带教医生ID',
  `order_type` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT '' COMMENT '医嘱类型',
  `order_time` time NULL DEFAULT NULL COMMENT '医嘱时间',
  `prescriber` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT '' COMMENT '开方人',
  `doctor_name` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT '' COMMENT '医生姓名',
  `nurse_name` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT '' COMMENT '护士姓名',
  `group_number` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '组号',
  `is_deleted` tinyint(1) NOT NULL DEFAULT 0 COMMENT '逻辑删除字段（0:未删除 1:已删除）',
  `create_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `create_by` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT '' COMMENT '创建人',
  `update_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '修改时间',
  `update_by` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT '' COMMENT '修改人',
  `estimated_expiration_date` date NULL DEFAULT NULL COMMENT '预计到期日期',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_unicode_ci COMMENT = '用药医嘱表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of medication_order
-- ----------------------------
INSERT INTO `medication_order` VALUES (1940355252354473984, '1939945677945696256', NULL, NULL, 'drug014', '阿司匹林肠溶片', '100mg*30片', '盒', 5, '', '口服', 'Qd', 2, NULL, '', '', NULL, '08:00:00', NULL, NULL, NULL, NULL, NULL, NULL, '', 22.5000, '每日一次，每次1片', '', '2025-07-02', NULL, '长期', '18:21:32', '', '100006', '', NULL, 1, '2025-07-02 18:21:56', 'admin', '2025-07-03 15:15:53', 'admin', NULL);
INSERT INTO `medication_order` VALUES (1940355297220943872, '1939945677945696256', NULL, NULL, 'drug015', '盐酸小檗碱片', '0.1g*100片', '瓶', 5, '', '口服', 'Tid', 1, NULL, '', '', NULL, '08:00:00', NULL, '12:00:00', NULL, '16:00:00', NULL, NULL, '', 11.0000, '每日三次，每次2片', '', '2025-07-02', NULL, '临时', '18:22:00', '', '', '', NULL, 1, '2025-07-02 18:22:07', 'admin', '2025-07-03 15:15:52', 'admin', NULL);
INSERT INTO `medication_order` VALUES (1940702534112915456, '1939945677945696256', 186575, '2025-07-11 13:14:32', 'drug016', '云南白药气雾剂', '85g+60g', '盒', 1, '1', '外用', 'Qd', 1, NULL, '', '', 1.00, '08:00:00', NULL, NULL, NULL, NULL, NULL, NULL, '', 38.0000, '早1片', '片', '2025-07-03', NULL, '长期', '17:20:06', 'admin', '100008', '', '3568', 0, '2025-07-03 17:21:59', 'admin', '2025-07-11 13:14:32', 'admin', NULL);
INSERT INTO `medication_order` VALUES (1940728463073775616, '1939945677945696256', NULL, NULL, 'drug015', '盐酸小檗碱片', '0.1g*100片', '瓶', 0, '', '口服', 'Biw', 1, NULL, '', '', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '', 11.0000, '每日三次，每次2片', '', '2025-07-03', NULL, '长期', '19:04:51', '', '100006', '', '', 1, '2025-07-03 19:05:01', 'admin', '2025-07-03 19:16:41', 'admin', NULL);
INSERT INTO `medication_order` VALUES (1940732378745966592, '1939945677945696256', 186575, '2025-07-11 13:14:32', 'drug015', '盐酸小檗碱片', '0.1g*100片', '瓶', 2, '', '口服', 'Qw', 1, NULL, '', '一，二', 1.00, '19:20:00', 2.00, NULL, NULL, NULL, NULL, NULL, '', 11.0000, '早1片，中2片', '片', '2025-07-03', NULL, '长期', '19:20:15', 'admin', 'admin', '', '', 0, '2025-07-03 19:20:34', 'admin', '2025-07-11 13:15:58', 'admin', NULL);
INSERT INTO `medication_order` VALUES (1940983461057671168, '1939945677945696256', NULL, NULL, 'drug014', '阿司匹林肠溶片', '100mg*30片', '盒', 5, '', '口服', 'Qd', 1, NULL, '', '', NULL, '08:00:00', NULL, NULL, NULL, NULL, NULL, NULL, '', 22.5000, '每日一次，每次1片', '', '2025-07-04', NULL, '长期', '11:58:07', '', '', '', '', 1, '2025-07-04 11:58:13', 'admin', '2025-07-04 17:24:35', 'admin', NULL);
INSERT INTO `medication_order` VALUES (1945049907503493120, '1944654938649325568', NULL, NULL, 'drug016', '云南白药气雾剂', '85g+60g', '盒', 5, '', '外用', 'Q8h', 1, NULL, '', '', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '', 38.0000, '每8小时一次，喷于患处适量', '', '2025-07-14', NULL, '长期', '15:06:15', NULL, '', '', '', 0, '2025-07-15 17:16:49', 'admin', '2025-07-21 13:49:08', 'admin', NULL);
INSERT INTO `medication_order` VALUES (1945049983764328448, '1944654938649325568', NULL, NULL, 'drug015', '盐酸小檗碱片', '0.1g*100片', '瓶', 5, '', '口服', 'Tid', 1, NULL, '', '', NULL, '08:00:00', NULL, '12:00:00', NULL, '16:00:00', NULL, NULL, '', 11.0000, '每日三次，每次2片', '', '2025-07-14', NULL, '临时', '15:06:15', NULL, '', '', '', 0, '2025-07-15 17:17:08', 'admin', '2025-07-21 13:49:08', 'admin', NULL);
INSERT INTO `medication_order` VALUES (1946827298992820224, '1946110438402342912', NULL, NULL, 'drug015', '盐酸小檗碱片', '0.1g*100片', '瓶', 5, '', '口服', 'Qw', 1, NULL, '', '日', 1.00, '14:59:10', NULL, NULL, NULL, NULL, NULL, NULL, '餐前', 11.0000, '早1片', '片', '2025-07-18', NULL, '长期', '15:30:33', NULL, '100006', '', '', 0, '2025-07-20 14:59:33', 'admin', '2025-07-22 11:49:13', 'admin', NULL);
INSERT INTO `medication_order` VALUES (1946828678612652032, '1946110438402342912', NULL, NULL, 'drug014', '阿司匹林肠溶片', '100mg*30片', '盒', 5, '', '口服', 'Biw', 2, NULL, '', '二，三', 1.00, '15:04:46', NULL, NULL, NULL, NULL, NULL, NULL, '餐时', 22.5000, '早1片', '片', '2025-07-18', NULL, '长期', '15:30:33', NULL, '', '', '', 0, '2025-07-20 15:05:02', 'admin', '2025-07-22 11:49:13', 'admin', NULL);
INSERT INTO `medication_order` VALUES (1946828823433580544, '1946110438402342912', NULL, NULL, 'drug008', '感冒灵颗粒', '10g*9袋', '盒', 5, '22', '口服', 'Tid', 1, NULL, '', '', 1.00, '08:00:00', 2.00, '12:00:00', 3.00, '16:00:00', NULL, NULL, '餐时', 13.5000, '早1袋，中2袋，晚3袋', '袋', '2025-07-18', NULL, '长期', '15:30:33', NULL, '', '', '', 0, '2025-07-20 15:05:36', 'admin', '2025-07-22 11:49:13', 'admin', NULL);
INSERT INTO `medication_order` VALUES (1946869967995617280, '1946110438402342912', NULL, NULL, 'drug006', '盐酸二甲双胍片', '0.5g*48片', '盒', 5, '2', '口服', 'Qod', 1, NULL, '', '', 1.00, '08:00:00', NULL, NULL, NULL, NULL, NULL, NULL, '', 24.0000, '早1片', '片', '2025-07-18', NULL, '长期', '15:30:33', NULL, '100008', '', '', 0, '2025-07-20 17:49:06', 'admin', '2025-07-22 11:49:13', 'admin', NULL);
INSERT INTO `medication_order` VALUES (1947619606021623808, '1939616071042895872', NULL, NULL, 'drug015', '盐酸小檗碱片', '0.1g*100片', '瓶', 5, '', '口服', 'Tiw', 1, NULL, '', '三，四', 25.00, '08:00:00', NULL, NULL, NULL, NULL, NULL, NULL, '', 11.0000, '早25片', '片', '2025-06-30', NULL, '长期', '17:23:46', NULL, 'admin', '', '', 1, '2025-07-22 19:27:54', 'admin', '2025-07-22 19:52:17', 'admin', NULL);
INSERT INTO `medication_order` VALUES (1947622991361748992, '1939616071042895872', NULL, NULL, 'drug014', '阿司匹林肠溶片', '100mg*30片', '盒', 5, '', '口服', 'Tiw', 1, NULL, '', '三，四，五', 5.00, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '', 22.5000, '早5片', '片', '2025-06-30', NULL, '长期', '17:23:46', NULL, '', '', '', 1, '2025-07-22 19:41:21', 'admin', '2025-07-22 19:52:18', 'admin', NULL);
INSERT INTO `medication_order` VALUES (1947625824098299904, '1939616071042895872', 414625, '2025-07-22 19:53:15', 'drug015', '盐酸小檗碱片', '0.1g*100片', '瓶', 1, '', '口服', 'Biw', 1, NULL, '', '三，四', NULL, '08:00:00', NULL, NULL, NULL, NULL, NULL, NULL, '', 11.0000, '25', '', '2025-06-30', NULL, '长期', '17:23:46', 'admin', 'admin', '', '', 0, '2025-07-22 19:52:36', 'admin', '2025-07-22 19:53:16', 'admin', '2025-07-31');

SET FOREIGN_KEY_CHECKS = 1;
